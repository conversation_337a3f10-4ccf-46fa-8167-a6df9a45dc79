import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { paymentSchema } from '@/lib/validations'

// GET /api/payments - Get all payments
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')

    const where: any = {}
    if (customerId) where.customerId = customerId

    const payments = await prisma.payment.findMany({
      where,
      include: {
        customer: true,
        customerDebt: {
          include: {
            product: true,
          },
        },
      },
      orderBy: { dateOfPayment: 'desc' },
    })

    return NextResponse.json(payments)
  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    )
  }
}

// POST /api/payments - Create a new payment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    const payment = await prisma.payment.create({
      data: {
        customerId: validatedData.customerId,
        customerDebtId: validatedData.customerDebtId,
        amount: validatedData.amount,
        notes: validatedData.notes,
        dateOfPayment: validatedData.dateOfPayment || new Date(),
      },
      include: {
        customer: true,
        customerDebt: {
          include: {
            product: true,
          },
        },
      },
    })

    return NextResponse.json(payment, { status: 201 })
  } catch (error) {
    console.error('Error creating payment:', error)
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid payment data', details: error },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    )
  }
}
